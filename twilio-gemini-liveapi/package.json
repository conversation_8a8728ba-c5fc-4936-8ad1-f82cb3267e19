{"name": "call_node_gemini", "version": "1.0.0", "type": "module", "imports": {"dotenv": "./src/utils/dotenv-stub.js"}, "scripts": {"start": "node --es-module-specifier-resolution=node index.js", "dev": "nodemon --exec 'node --es-module-specifier-resolution=node index.js'", "debug": "nodemon --exec 'node --inspect --es-module-specifier-resolution=node index.js'", "test": "NODE_ENV=test node --test test/", "test:session-fixes": "node test-session-fixes.js", "test:4-flows": "NODE_ENV=test node --test test/session-handling-fixes.test.js", "test:workflow": "NODE_ENV=test node --test test/workflow-integration.test.js", "test:all": "npm run test && npm run test:session-fixes", "lint": "eslint *.js", "lint:fix": "eslint *.js --fix", "audit": "pnpm audit", "update": "pnpm update", "clean": "rm -rf node_modules audio-debug data/*.json", "clean:audio": "rm -rf audio-debug", "health": "curl -s http://localhost:${PORT:-3101}/health | jq", "audio-quality": "curl -s http://localhost:${PORT:-3101}/api/audio-quality | jq", "monitor": "concurrently \"pnpm run dev\" \"pnpm run health\" \"pnpm run audio-quality\""}, "dependencies": {"@deepgram/sdk": "^4.7.0", "@fastify/compress": "^8.1.0", "@fastify/cors": "^11.0.1", "@fastify/formbody": "latest", "@fastify/helmet": "^13.0.1", "@fastify/rate-limit": "^10.3.0", "@fastify/static": "^8.1.1", "@fastify/websocket": "latest", "@google/genai": "^0.9.0", "@google/generative-ai": "^0.21.0", "@supabase/supabase-js": "^2.50.2", "@tensorflow/tfjs-node": "^4.22.0", "@types/node": "^22.15.32", "audio-buffer-utils": "^5.1.2", "audio-context-polyfill": "^1.0.0", "audio-decode": "^2.2.3", "audio-lena": "^2.3.0", "compression": "^1.8.0", "countries-and-timezones": "^3.7.2", "dotenv": "latest", "fastify": "latest", "form-data": "^4.0.1", "google-auth-library": "^9.15.1", "jsonwebtoken": "^9.0.2", "libphonenumber-js": "^1.12.6", "ngrok": "^5.0.0-beta.2", "node-fetch": "^3.3.2", "node-wav": "^0.0.2", "pcm-convert": "^1.6.5", "speex-resampler": "^3.0.1", "twilio": "latest", "web-audio-api": "^0.2.2", "ws": "latest", "zod": "^3.23.8"}, "devDependencies": {"concurrently": "^9.2.0", "eslint": "^9.29.0", "jsdom": "^25.0.1", "nodemon": "^3.1.10", "sinon": "^21.0.0"}}